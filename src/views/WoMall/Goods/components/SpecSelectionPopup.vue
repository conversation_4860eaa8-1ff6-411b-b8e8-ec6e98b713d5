<template>
  <van-popup :show="visible" position="bottom" round @close="handleClose"
    @update:show="(value) => emit('update:visible', value)">
    <div class="spec-popup">
      <div class="popup-header">
        <h3 class="popup-title">选择规格</h3>
        <div class="close-btn" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="#999" stroke-width="2" stroke-linecap="round" />
          </svg>
        </div>
      </div>

      <div class="goods-info">

        <div class="goods-detail">
          <img :src="goodsInfo.image" alt="商品图片" class="goods-image" />
          <div class="goods-content">
            <PriceDisplay :price="goodsInfo.price" size="large" color="orange" />
            <div class="quantity-section">
              <van-stepper v-model="quantity" :min="quantityRange.min" :max="quantityRange.max"
                :disabled="quantityRange.max === 0" @change="handleQuantityChange" />
              <div class="purchase-limit">{{ purchaseLimitText }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="spec-section">
        <div class="spec-header">
          <span class="spec-title">规格</span>
          <div class="goods-code">
            <span class="code-label">商品编码：</span>
            <span class="code-value">{{ goodsInfo.supplierSkuId }}</span>
            <img src="../assets/copy.png" alt="复制" class="copy-icon" width="16" height="16" @click="handleCopyCode" />
          </div>
        </div>

        <div class="spec-options">
          <div class="radio-wrapper" v-for="(specs, groupIndex) in displaySpecsList" :key="groupIndex">
            <div class="spec-group-title" v-if="specs.length > 0">
              {{ getSpecGroupName(groupIndex) }}
            </div>
            <button v-for="(spec, specIndex) in specs" :key="specIndex"
              :class="{ active: specOptions.curSpecs.indexOf(spec) >= 0, disabled: specOptions.curDisabledSpecs.indexOf(spec) >= 0 }"
              @click="selectSpec(spec)">
              {{ removeSpecPrefix(spec) }}
            </button>
          </div>
        </div>
      </div>

      <WoActionBar>
        <div class="action-buttons">
          <WoButton v-if="!hasSpecs" block type="gradient" size="large" class="confirm-btn"
            :disabled="cartButtonDisabled" @click="handleConfirm">
            确定
          </WoButton>
          <template v-else>
            <WoButton type="gradient" size="large" class="add-cart-btn"
              :disabled="cartButtonDisabled" @click="handleAddToCart">
              加入购物车
            </WoButton>
            <WoButton type="gradient" size="large" class="buy-now-btn"
              :disabled="cartButtonDisabled" @click="handleBuyNow">
              立即购买
            </WoButton>
          </template>
        </div>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, nextTick, toRefs, shallowRef } from 'vue'
import { debounce, memoize } from 'lodash-es'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import useClipboard from 'vue-clipboard3'
import { showToast } from 'vant'
import { removeSpecPrefix } from '@/utils/goodsDetail'
const { toClipboard } = useClipboard()

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  addressInfo: {
    type: Object,
    required: true
  },
  goodsInfo: {
    type: Object,
    required: true
  },
  specOptions: {
    type: Object,
    required: true
  },
  initialQuantity: {
    type: Number,
    required: true
  },
  actionType: {
    type: Number,
    required: true,
    validator: (value) => value === 1 || value === 2
  },
  cartButtonDisabled: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'select-spec', 'add-to-cart', 'buy-now', 'confirm', 'quantity-change'])

const {
  visible,
  addressInfo,
  goodsInfo,
  specOptions,
  initialQuantity,
  actionType,
  cartButtonDisabled
} = toRefs(props)

const quantity = shallowRef(initialQuantity.value)

const handleCopyCode = async () => {
  try {
    await toClipboard(goodsInfo.value.supplierSkuId)
    showToast('复制成功');
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败');
  }
}
const addressText = computed(() => {
  const { provinceName, cityName, countyName, townName, addrDetail } = addressInfo.value
  const locationText = `${provinceName || ''} ${cityName || ''} ${countyName || ''} ${townName || ''}`.trim()
  const fullAddress = addrDetail ? `${locationText} ${addrDetail}` : locationText
  return fullAddress || '配送地址'
})

const purchaseLimitText = computed(() => {
  const { xgObj, lowestBuyObj, purchaseLimitText, stock } = goodsInfo.value

  const messages = []

  if (stock <= 0) {
    messages.push('暂无库存')
  }

  if (lowestBuyObj?.isLowestBuy) {
    messages.push(lowestBuyObj.lowestBuyText)
  }

  if (xgObj?.isXg && xgObj?.limitText) {
    messages.push(xgObj.limitText)
  }

  if (purchaseLimitText) {
    messages.push(purchaseLimitText)
  }

  return messages.join('，')
})
const quantityRange = computed(() => {
  const { xgObj, lowestBuyObj, stock } = goodsInfo.value

  let min = 1
  if (lowestBuyObj?.isLowestBuy) {
    min = lowestBuyObj.lowestBuyNum
  }

  let max = 999

  if (stock > 0) {
    max = Math.min(max, stock)
  }

  if (xgObj?.isXg && xgObj?.limitNum) {
    max = Math.min(max, xgObj.limitNum)
  }

  return { min, max }
})
watch(goodsInfo, () => {
  const { min, max } = quantityRange.value

  if (quantity.value < min) {
    quantity.value = min
  }

  if (quantity.value > max) {
    quantity.value = max
  }
}, { deep: true })

watch(initialQuantity, (newVal) => {
  const { min } = quantityRange.value
  quantity.value = Math.max(newVal, min)
})
watch(quantity, (newVal) => {
  emit('quantity-change', newVal)
})
const handleClose = () => {
  emit('update:visible', false)
}

const selectSpec = (spec) => {
  const { curDisabledSpecs, curSpecs } = specOptions.value

  if (curDisabledSpecs.indexOf(spec) >= 0) return

  if (spec === '默认规格' && curSpecs.indexOf(spec) >= 0) {
    return
  }

  emit('select-spec', spec)
}
const handleQuantityChange = debounce((value) => {
  const { min, max } = quantityRange.value
  const { xgObj, lowestBuyObj, stock } = goodsInfo.value

  if (value < min) {
    if (lowestBuyObj?.isLowestBuy) {
      showToast(`最少购买${min}件哦！`)
    }
    quantity.value = min
    return
  }

  if (value > max) {
    if (stock > 0 && value > stock) {
      showToast(`库存不足，仅剩${stock}件`)
    } else if (xgObj?.isXg && value > xgObj.limitNum) {
      showToast(`超出限购数量：${xgObj.limitText}`)
    }
    quantity.value = max
    return
  }

  quantity.value = value
}, 100)

const createSkuInfo = () => ({
  quantity: quantity.value,
  selectedSpecs: specOptions.value.curSpecs,
  goodsId: goodsInfo.value.goodsId,
  supplierSkuId: goodsInfo.value.supplierSkuId,
  price: goodsInfo.value.price,
  stock: goodsInfo.value.stock,
  currSku: goodsInfo.value.currSku,
})

const handleAddToCart = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  emit('add-to-cart', createSkuInfo())
}

const handleBuyNow = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  emit('buy-now', createSkuInfo())
}

const handleConfirm = () => {
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  const skuInfo = createSkuInfo()

  if (actionType.value === 2) {
    emit('buy-now', skuInfo)
  } else {
    emit('add-to-cart', skuInfo)
  }
}

const hasSpecs = computed(() => {
  const specs = specOptions.value
  return specs && specs.specsList && specs.specsList.length > 0 && specs.specsList[0].length > 0
})

const displaySpecsList = computed(() => {
  return hasSpecs.value ? specOptions.value.specsList : [['默认规格']]
})

const getSpecGroupName = memoize((groupIndex) => {
  return hasSpecs.value ? `规格${groupIndex + 1}` : '规格'
})
const isSpecsComplete = () => {
  const { specsList = [], curSpecs = [] } = specOptions.value

  if (!hasSpecs.value) {
    return true
  }

  const validSpecsGroupCount = specsList.reduce((count, group) => {
    return group && group.length > 0 ? count + 1 : count
  }, 0)

  return validSpecsGroupCount === curSpecs.length
}
watch(visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      if (!hasSpecs.value && specOptions.value.curSpecs.length === 0) {
        emit('select-spec', '默认规格')
      }
    })
  }
})


</script>

<style scoped lang="less">
.spec-popup {
  background-color: @bg-color-white;
  border-radius: @radius-12 @radius-12 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 70px;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    flex-shrink: 0;

    .popup-title {
      font-size: @font-size-16;
      color: @text-color-primary;
      font-weight: @font-weight-500;
      margin: 0;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .goods-info {
    padding: 0 17px 10px 17px;
    flex-shrink: 0;
    box-sizing: border-box;

    .goods-basic {
      margin-bottom: 16px;

      .address-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;

        .address-content {
          flex: 1;

          .receiver-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 4px;

            .receiver-name {
              font-size: @font-size-14;
              color: @text-color-secondary;
              font-weight: @font-weight-400;
            }

            .receiver-phone {
              font-size: @font-size-14;
              color: @text-color-secondary;
              font-weight: @font-weight-400;
            }
          }

          .address-detail {
            .address-text {
              font-size: @font-size-15;
              color: @text-color-primary;
              font-weight: @font-weight-600;
              line-height: 1.4;
            }
          }
        }

        .arrow-right {
          width: 6px;
          height: 12px;
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }

    .goods-detail {
      display: flex;
      gap: 12px;

      .goods-image {
        width: 80px;
        height: 80px;
        border-radius: @radius-8;
        object-fit: cover;
        flex-shrink: 0;
      }

      .goods-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .price-display {
          margin-bottom: 8px;
        }

        .quantity-section {
          display: flex;
          align-items: center;
          justify-content: space-between;



          .purchase-limit {
            font-size: @font-size-13;
            color: #ff6b35;
          }
        }
      }
    }
  }

  .spec-section {
    padding: 10px 17px 0 17px;
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;

    .spec-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .spec-title {
        font-size: @font-size-16;
        color: @text-color-primary;
        font-weight: @font-weight-500;
      }

      .goods-code {
        display: flex;
        align-items: center;
        gap: 4px;

        .code-label {
          font-size: @font-size-12;
          color: #999;
        }

        .code-value {
          font-size: @font-size-12;
          color: #999;
        }

        .copy-icon {
          width: 10px;
          height: 10px;
          flex-shrink: 0;
          margin-left: 2px;
          cursor: pointer;
        }
      }
    }

    .spec-options {
      flex: 1;
      overflow-y: scroll;
      max-height: 300px;
      min-height: 150px;

      .radio-wrapper {
        line-height: 40px;

        .spec-group-title {
          font-size: @font-size-14;
          color: @text-color-primary;
          font-weight: @font-weight-500;
          margin-bottom: 8px;
          margin-top: 16px;

          &:first-child {
            margin-top: 0;
          }
        }

        .specs-button-division-line {
          width: 100%;
          height: 1px;
          background: rgba(229, 229, 229, 0.64);
        }

        &:last-child .specs-button-division-line {
          height: 0;
        }

        button {
          display: inline-block;
          min-width: 75px;
          font-size: @font-size-13;
          color: @text-color-primary;
          line-height: 1.2;
          background: #F7F7F7;
          border-radius: 4px;
          padding: 8px 12px;
          margin-right: 8px;
          margin-bottom: 8px;
          outline: none;
          border: 1px solid transparent;
          cursor: pointer;
          transition: all 0.2s ease;

          &.active {
            background: rgba(255, 120, 10, 0.10);
            border: 1px solid #FF780A;
            color: #FF780A;
          }

          &.disabled {
            background: #F7F7F7;
            color: #B1BEC9;
            cursor: not-allowed;
          }


        }
      }
    }
  }

  .action-buttons {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .add-cart-btn {
      background-image: linear-gradient(90deg, #FFC72D 0%, #FFAD1B 100%);
    }

    .buy-now-btn {
      background-image: linear-gradient(101deg, #FFA033 0%, #FF6D33 100%);
    }
  }
}
</style>
